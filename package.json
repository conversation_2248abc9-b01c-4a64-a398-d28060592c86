{"name": "city-land-prs-backend", "scripts": {"start": "npm run migrate:dev && node index.js", "start:prod": "npm run migrate:prod && node index.js", "dev": "npm run migrate:dev && nodemon index.js", "migrate-and-seed": "npm run migrate:dev && npm run seed:dev", "migrate:dev": "cross-env NODE_PATH=. NODE_ENV=development sequelize --options-path ./.sequelizerc db:migrate", "migrate:dev:undo": "cross-env NODE_PATH=. NODE_ENV=development sequelize --options-path ./.sequelizerc db:migrate:undo --name", "migrate:dev:undo:all": "cross-env NODE_PATH=. NODE_ENV=development sequelize --options-path ./.sequelizerc db:migrate:undo:all", "seed:dev": "cross-env NODE_PATH=. NODE_ENV=development sequelize db:seed:all", "seed:dev:per-file": "cross-env NODE_PATH=. NODE_ENV=development sequelize db:seed --seed", "seed:prod:per-file": "cross-env NODE_PATH=. NODE_ENV=production sequelize db:seed --seed", "seed:dev:undo": "cross-env NODE_PATH=. NODE_ENV=development sequelize db:seed:undo --seed", "seed:dev:undo:all": "cross-env NODE_PATH=. NODE_ENV=development sequelize db:seed:undo:all", "generate:migration": "cross-env NODE_PATH=. NODE_ENV=development sequelize --options-path ./.sequelizerc migration:generate --name", "generate:seeder": "cross-env NODE_PATH=. NODE_ENV=development sequelize --options-path ./.sequelizerc seed:generate --name", "migrate:prod": "NODE_PATH=. NODE_ENV=production sequelize --options-path ./.sequelizerc db:migrate", "migrate:prod:undo": "NODE_PATH=. NODE_ENV=production sequelize --options-path ./.sequelizerc db:migrate:undo", "seed:prod": "cross-env NODE_PATH=. NODE_ENV=production sequelize db:seed:all", "fix": "npm run fix:eslint && npm run fix:prettier", "fix:eslint": "eslint --fix .", "fix:prettier": "prettier --write .", "lint": "npm run lint:eslint && npm run lint:prettier", "lint:eslint": "eslint --cache --cache-location node_modules --report-unused-disable-directives --max-warnings 1 .", "lint:prettier": "prettier --check .", "run:view-rs:migration": "node scripts/runRsSearchView.js", "cleanup:roles": "node scripts/alignRolePermissions.js", "cleanup:delivery-receipts": "node scripts/cleanDeliveryReceiptsData.js", "cleanup:floating-attachments": "node scripts/cleanFloatingAttachments.js", "cleanup:align-assigned-to-po": "node scripts/alignAssignedToPurchaseOrder.js", "timescaledb:status": "node scripts/manageTimescaleDB.js status", "cleanup:invoice-reports": "node scripts/cleanInvoiceReports.js", "cleanup:reset-delivery-receipts": "node scripts/resetDeliveryReceipts.js", "cleanup:payment-requests": "node scripts/cleanPaymentRequests.js", "cleanup:reset-pr-approval": "node scripts/resetPaymentRequestApproval.js", "delete:old-attachments": "node scripts/deleteOldAttachments.js", "prepare": "if [ \"$NODE_ENV\" == \"local\" ]; then husky install; fi", "test": "mocha", "format": "prettier --write \"src/**/*.js\"", "format:check": "prettier --check \"src/**/*.js\""}, "dependencies": {"@fastify/auth": "^5.0.1", "@fastify/awilix": "^6.0.1", "@fastify/compress": "^8.0.1", "@fastify/cors": "^10.0.1", "@fastify/helmet": "^13.0.1", "@fastify/jwt": "^9.0.1", "@fastify/multipart": "^9.0.1", "@fastify/postgres": "^6.0.1", "@fastify/rate-limit": "^10.2.2", "@fastify/request-context": "^6.0.2", "@fastify/swagger": "^9.1.0", "@fastify/swagger-ui": "^5.0.1", "awilix": "^12.0.1", "bcryptjs": "^2.4.3", "bullmq": "^5.56.4", "dotenv": "^16.4.5", "fastify": "^5.0.0", "fastify-multer": "^2.0.3", "fastify-postgres": "^3.6.0", "handlebars": "^4.7.8", "ioredis": "^5.6.1", "json-2-csv": "^5.5.8", "juice": "^11.0.0", "nodemailer": "^6.9.15", "otpauth": "^9.3.4", "pdf-lib": "^1.17.1", "pg": "^8.13.0", "pino": "^9.4.0", "pino-pretty": "^11.2.2", "puppeteer": "^24.9.0", "rotating-file-stream": "^3.2.5", "scqs-sheets-stratpoint": "^0.6.2", "sequelize": "^6.37.4", "uuid": "^10.0.0", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.21.0", "chai": "^4.3.10", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "eslint": "^9.21.0", "eslint-config-stratpoint": "^3.0.0", "globals": "^16.0.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "mocha": "^10.7.3", "nodemon": "^3.1.7", "nyc": "^17.1.0", "prettier": "^3.3.3", "sequelize-cli": "^6.6.2", "sinon": "^19.0.2", "sinon-chai": "^3.7.0"}, "engines": {"node": ">= 6.9.4", "npm": ">= 4.4.0"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "nyc": {"reporter": ["text", "html"], "extension": [".js"], "include": ["src/**/*.js"], "exclude": ["test/**/*.js"]}, "lint-staged": {"*.js": "eslint --cache --cache-location node_modules --report-unused-disable-directives --max-warnings 1", "*.{js,json,jsonc,yml,yaml,md}": "prettier --check"}}