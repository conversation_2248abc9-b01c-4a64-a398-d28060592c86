const multer = require('fastify-multer');
const path = require('path');
const fs = require('fs');

const uploadFile = async function (request, _reply) {
  const clientErrors = this.diScope.resolve('clientErrors');
  const remainingFileLimit = request.remainingFileLimit
    ? Math.max(0, request.remainingFileLimit)
    : -1;

  try {
    const storage = multer.memoryStorage();

    const upload = multer({
      storage,
      fileFilter: function (req, file, cb) {
        const allowedMimes = [
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
          'application/vnd.ms-excel', // xls
          'text/csv',
          'application/pdf',
          'image/jpeg',
          'image/jpg',
          'image/png',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ];

        if (!allowedMimes.includes(file.mimetype)) {
          return cb(
            new Error(
              'Invalid file type. Only PNG, JPG, JPEG, PDF, Excel, and CSV are allowed.',
            ),
            false,
          );
        }
        cb(null, true);
      },
      limits: {
        files: remainingFileLimit,
        // fileSize: 10 * 1024 * 1024, // 10 MB
      },
    });

    // Convert upload.array to a promise for async/await
    const uploadFiles = () => {
      return new Promise((resolve, reject) => {
        upload.array('attachments')(request, _reply, (err) => {
          if (err) {
            reject(err);
          }

          // Attach files to request.body.attachments
          const filesWithPath = request.files.map((file) => {
            const first13Chars = file.originalname.slice(0, 13);
            const isFirst13Digits = /^\d{13}$/.test(first13Chars);
            if (isFirst13Digits) {
              file.originalname = `${file.originalname}`;
            } else {
              file.originalname = `${Date.now()}${file.originalname}`;
            }
            const filePath = file.originalname;
            return { ...file, filePath };
          });
          request.body.attachments = filesWithPath;
          resolve(filesWithPath);
        });
      });
    };

    // Process files with streams after uploading
    const files = await uploadFiles();
    for (const file of files) {
      const destinationFolder = request.body.model
        ? `./upload/${request.body.model}`
        : `./upload/${request.url.split('/')[2]}`;

      if (!fs.existsSync(destinationFolder)) {
        fs.mkdirSync(destinationFolder, { recursive: true });
      }

      const destPath = path.join(destinationFolder, file.filePath);

      // Stream the file buffer to disk
      const writeStream = fs.createWriteStream(destPath);
      writeStream.write(file.buffer);
      writeStream.end();

      await new Promise((resolve, reject) => {
        writeStream.on('finish', resolve);
        writeStream.on('error', reject);
      });
    }
  } catch (error) {
    if (error.code === 'LIMIT_FILE_COUNT') {
      const errorMessage = `Upload limit exceeded. You can only upload up to ${remainingFileLimit} more files.`;
      const additionalMessage =
        remainingFileLimit > 0
          ? ' Please remove some files and try again.'
          : ' No more files can be uploaded at this time.';

      throw clientErrors.BAD_REQUEST({
        message: `${errorMessage}${additionalMessage}`,
      });
    } else if (error.code === 'LIMIT_FILE_SIZE') {
      throw clientErrors.BAD_REQUEST({
        message: 'File size limit exceeded. Maximum file size is 25 MB.',
      });
    } else {
      throw clientErrors.BAD_REQUEST({
        message: error.message,
      });
    }
  }
};

module.exports = uploadFile;
