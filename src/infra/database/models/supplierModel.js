const { EventEmitter } = require('events');
const supplierEvents = new EventEmitter();
module.exports = (sequelize, Sequelize) => {
  const SupplierModel = sequelize.define(
    'suppliers',
    {
      id: {
        type: Sequelize.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
      userId: {
        type: Sequelize.INTEGER,
        allowNull: false,
        references: {
          model: 'users',
          key: 'id',
        },
        field: 'user_id',
      },
      name: {
        type: Sequelize.STRING(100),
        allowNull: false,
      },
      contact: {
        type: Sequelize.STRING(20),
        allowNull: true,
      },
      tin: {
        type: Sequelize.STRING(15),
        allowNull: false,
      },
      address: {
        type: Sequelize.TEXT,
        allowNull: false,
      },
      contactPerson: {
        type: Sequelize.STRING(100),
        allowNull: true,
        field: 'contact_person',
      },
      contactNumber: {
        type: Sequelize.STRING(20),
        allowNull: true,
        field: 'contact_number',
      },
      citizenshipCode: {
        type: Sequelize.STRING(2),
        allowNull: false,
        field: 'citizenship_code',
      },
      natureOfIncome: {
        type: Sequelize.STRING(20),
        allowNull: false,
        field: 'nature_of_income',
      },
      payCode: {
        type: Sequelize.STRING(4),
        allowNull: false,
        field: 'pay_code',
        unique: true
      },
      lineOfBusiness: {
        type: Sequelize.STRING(50),
        allowNull: true,
        field: 'line_of_business',
      },
      iccode: {
        type: Sequelize.STRING(2),
        allowNull: false,
        field: 'ic_code',
      },
      status: {
        type: Sequelize.STRING,
        allowNull: true,
      },
    },
    {
      paranoid: true,
      timestamps: true,
      underscored: true,
      hooks: {
        beforeUpdate: (supplier) => {
          supplier.updatedAt = new Date();
        },
      },
    },
  );

  /* -------------------------------------------------------------------------- */
  /*                                Associations                                */
  /* -------------------------------------------------------------------------- */

  SupplierModel.associate = (models) => {
    SupplierModel.hasMany(models.attachmentModel, {
      foreignKey: 'model_id',
      as: 'attachments',
    });

    SupplierModel.hasMany(models.commentModel, {
      foreignKey: 'model_id',
      as: 'comments',
    });

    SupplierModel.hasMany(models.nonRequisitionModel, {
      foreignKey: 'supplier_id',
      as: 'nonRS',
    });
  };

  return SupplierModel;
};
