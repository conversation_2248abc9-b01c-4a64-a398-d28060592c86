'use strict';
const { USER_TYPES } = require('../../../domain/constants/userConstants');

module.exports = {
  async up(queryInterface, Sequelize) {
    const roles = await queryInterface.sequelize.query(
      `SELECT id, name FROM roles WHERE name = :name`,
      {
        replacements: { name: USER_TYPES.PURCHASING_STAFF },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    if (!roles.length) {
      console.log('Purchasing Staff role not found');
      return;
    }

    /**
     * Purchasing Staff
     * - Most modules
     * - Except dashboard approval
     * - View & get canvass only
     * - View & get orders only
     * - View & get orders only
     */

    const adminRole = roles[0];
    const permissions = await queryInterface.sequelize.query(
      `SELECT id, module, action 
       FROM permissions 
       WHERE module IN ('suppliers')
       `,
      {
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    /* Create role permissions array for Purchasing Staff */
    const dateNow = new Date();
    const rolePermissions = permissions.map((permission) => ({
      role_id: adminRole.id,
      permission_id: permission.id,
      created_at: dateNow,
      updated_at: dateNow,
    }));

    /* Check existing role permissions */
    const existingRolePermissions = await queryInterface.sequelize.query(
      `SELECT role_id, permission_id 
       FROM role_permissions 
       WHERE role_id = :roleId`,
      {
        replacements: { roleId: adminRole.id },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    /* Filter out existing role permissions */
    const newRolePermissions = rolePermissions.filter(
      (rp) =>
        !existingRolePermissions.some(
          (existing) => existing.permission_id === rp.permission_id,
        ),
    );

    if (newRolePermissions.length > 0) {
      console.log('Purchasing Staff - permissions to add', newRolePermissions);
      await queryInterface.bulkInsert(
        'role_permissions',
        newRolePermissions,
        {},
      );
      console.log(
        `Added ${newRolePermissions.length} permissions to Purchasing Staff role`,
      );
    } else {
      console.log('No new permissions to add');
    }

    /* Log assigned permissions for verification */
    console.log('Purchasing Staff permissions:');
    permissions.forEach((p) => {
      console.log(`- ${p.module} ${p.action}`);
    });
  },

  async down(queryInterface, Sequelize) {
    const role = await queryInterface.sequelize.query(
      `SELECT id FROM roles WHERE name = :name`,
      {
        replacements: { name: USER_TYPES.ADMIN },
        type: queryInterface.sequelize.QueryTypes.SELECT,
      },
    );

    if (role.length) {
      await queryInterface.bulkDelete(
        'role_permissions',
        {
          role_id: role[0].id,
        },
        {},
      );
      console.log('Removed all Purchasing Staff role permissions');
    }
  },
};
