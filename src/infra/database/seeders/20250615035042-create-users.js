'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    const bcrypt = require('bcryptjs');
    const { generateTemporaryPassword } = require('../../../app/utils/index');

    const { users } =
      process.env.NODE_ENV === 'production'
        ? require('./data/users')
        : require('./data/users_staging');

    console.log(`Seeding ${users.length} users`);

    const existingUsers = await queryInterface.select(null, 'users', {
      where: {
        username: {
          [Sequelize.Op.in]: users.map((user) => user.username),
        },
      },
    });

    const newUsers = users.filter(
      (user) =>
        !existingUsers.some(
          (existingUser) => existingUser.username === user.username,
        ),
    );

    if (newUsers.length === 0) {
      console.log('All users already exist, nothing to seed!');
      return;
    }

    const roles = await queryInterface.select(null, 'roles');
    const parsedRoles = roles.reduce((acc, role) => {
      acc[role.name] = role.id;
      return acc;
    }, {});

    const newUsersWithRoles = newUsers.map((user) => {
      const { role_name, ...rest } = user;
      const defaultPassword = generateTemporaryPassword().substring(0, 9);
      const hashedPassword = bcrypt.hashSync(defaultPassword, 8);
      return {
        ...rest,
        role_id: parsedRoles[user.role_name],
        temp_pass: user.temp_pass ?? defaultPassword,
        password: user.password ?? hashedPassword,
      };
    });

    console.log(`Seeding ${newUsersWithRoles.length} new users`);

    await queryInterface.bulkInsert('users', newUsersWithRoles);
  },

  async down(queryInterface, Sequelize) {
    // do nothing
  },
};
