const { z } = require('zod');
const { sort, requisition, filter, note } = require('../constants');
const { sortSchemaV2, sortSchema } = require('./sortEntity');
const { filterSchema } = require('./filterEntity');
const {
  createIdParamsSchema,
  stringFieldError,
  createNumberSchema,
  createOptionalIdParamsSchema,
} = require('../../app/utils');
const { normalize } = require('./utils')

const requisitionSortSchema = sortSchemaV2(sort.REQUISITION_SORT_COLUMNS);
const requisitionFilterSchema = filterSchema(filter.REQUISITION_FILTER_COLUMNS);
const isoDateRegex = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/;

const REQUIRED_FIELD_ERROR =
  'All fields are required. Kindly check your entered data';

const createRequisitionSchema = z
  .object({
    isDraft: z
      .enum(['true'], {
        required_error: 'Draft status is required',
        invalid_type_error: 'Invalid draft status',
      })
      .transform((value) => value === 'true'),
    type: z.enum(['ofm', 'non-ofm', 'ofm-tom', 'non-ofm-tom', 'transfer'], {
      message: REQUIRED_FIELD_ERROR,
    }),
    // transfer = transfer of materials
    companyId: createIdParamsSchema('Company'),
    departmentId: createIdParamsSchema('Department'),
    projectId: createOptionalIdParamsSchema('Project').or(z.literal('')),
    dateRequired: z.string().date(),
    deliverTo: z.string().min(1, { message: `Deliver to is Required` }),
    purpose: z
      .string()
      .min(1, 'Create RS Purpose must not be Empty')
      .max(100, 'Create RS Purpose maximum 100 characters')
      .regex(note.REGEX, 'RS Purpose contains invalid characters.'),
    chargeTo: z
      .enum(requisition.CHARGE_TO_LIST, {
        message: 'Invalid charge to (category)',
      })
      .optional()
      .or(z.literal('')),
    chargeToId: createOptionalIdParamsSchema('Charge to')
      .optional()
      .or(z.literal(''))
      .or(z.undefined()),
    itemList: z.array(
      z.object({
        id: z.number().positive().optional().or(z.string().transform(Number)),
        ofmListId: createNumberSchema('OFM List').optional(),
        quantity: z
          .number({
            errorMap: () => ({ message: 'Item Quantity Cannot Be Empty' }),
          })
          .min(0.001, 'Item Quantity Cannot Be Empty')
          .positive()
          .or(
            z
              .string()
              .min(1, 'Item Quantity Cannot Be Empty')
              .regex(note.REGEX, 'Item Quantity contains invalid characters.')
              .transform(Number),
          ),
        itemType: z.string().optional(),
        acctCd: z.string().optional(),
        notes: z
          .string()
          .max(500, 'Maximum of 500 Characters')
          .regex(note.REGEX, 'Item Note contains invalid characters.')
          .nullable()
          .optional(),
        itemName: z.string().optional(),
        unit: z.string().optional(),
        companyCode: z.number().int().positive().nullable().optional(),
        projectCode: z.string().nullable().optional(),
        trade_code: z.number().positive().optional(),
        description: z.string().optional(),
        createdAt: z.string().optional(),
        updatedAt: z.string().optional(),
        company: z
          .string()
          .nullable()
          .optional()
          .or(
            z.object({
              code: z.string().nullable().optional(),
              name: z.string().nullable().optional(),
            }),
          ),
        project: z
          .string()
          .nullable()
          .optional()
          .or(
            z.object({
              code: z.string().nullable().optional(),
              name: z.string().nullable().optional(),
            }),
          ),
        trade: z
          .object({
            name: z.string().optional(),
            code: z.number().int().positive().optional(),
          })
          .optional(),
        itemCd: z.string().optional(),
        itmDes: z.string().optional(),
        gfq: z.number().positive().optional(),
        tradeCode: z.number().positive().optional(),
        remainingGfq: z.number().positive().nullable().optional(),
      }),
    ),
    comment: z
      .string({
        required_error: 'RS Creation note is required',
        invalid_type_error: 'RS Creation note must be a string',
      })
      // .max(100, 'Maximum of 100 Characters')
      .trim()
      .regex(note.REGEX, 'RS Note contains invalid characters.')
      .refine(
        (value) => value.replace(/[\r\n]/g, '').length <= 100,
        'Requisition Notes has Maximum of 100 Characters',
      )
      .optional(),
    category: z.enum(requisition.REQUISITION_CATEGORIES, {
      message: 'Invalid category',
    }),
  })
  .strict();

const submitRequisition = z
  .object({
    id: z
      .number()
      .positive()
      .optional()
      .or(z.string().transform(Number).optional()),
    isDraft: z.enum(['false'], {
      required_error: 'Draft status is required',
      invalid_type_error: 'Invalid draft status',
    }),
    type: z.enum(['ofm', 'non-ofm', 'ofm-tom', 'non-ofm-tom', 'transfer'], {
      message: REQUIRED_FIELD_ERROR,
    }),
    companyId: createIdParamsSchema(REQUIRED_FIELD_ERROR, true),
    departmentId: createIdParamsSchema(REQUIRED_FIELD_ERROR, true),
    projectId: createOptionalIdParamsSchema('Project').or(z.literal('')),
    dateRequired: z
      .string({ message: REQUIRED_FIELD_ERROR })
      .date(REQUIRED_FIELD_ERROR),
    deliverTo: z
      .string({ message: REQUIRED_FIELD_ERROR })
      .min(1, REQUIRED_FIELD_ERROR),
    purpose: z
      .string({ message: REQUIRED_FIELD_ERROR })
      .min(1, REQUIRED_FIELD_ERROR)
      .max(100, 'Create RS Purpose maximum 100 characters')
      .regex(note.REGEX, 'RS Purpose contains invalid characters.'),
    chargeTo: z
      .enum(requisition.CHARGE_TO_LIST, {
        message: REQUIRED_FIELD_ERROR,
      })
      .optional()
      .or(z.literal('')),
    chargeToId: createOptionalIdParamsSchema('Charge to')
      .optional()
      .or(z.literal(''))
      .or(z.undefined()),
    category: z
      .enum(requisition.REQUISITION_CATEGORIES, {
        message: 'Invalid category',
      })
      .optional(),
    itemList: z.array(
      z.object({
        id: z
          .number({ message: REQUIRED_FIELD_ERROR })
          .positive()
          .optional()
          .or(z.string().transform(Number)),
        ofmListId: createNumberSchema('OFM List ID').optional(),
        quantity: z
          .number({
            errorMap: () => ({ message: 'Item quantity is required' }),
          })
          .min(0.001, 'Item quantity must be greater than 0')
          .positive()
          .or(z.string().min(1, REQUIRED_FIELD_ERROR).transform(Number)),
        itemType: z.string().optional(),
        acctCd: z.string().optional(),
        notes: z
          .string()
          .transform(v => normalize(v))
          .pipe(
            z.string()
              .max(500, 'Maximum of 500 Characters')
              .regex(
                note.REGEX,
                'Item Notes contains invalid characters.'
              )
          )
          .nullable()
          .optional(),
        itemName: z.string().optional(),
        unit: z.string().optional(),
        companyCode: z.number().int().positive().nullable().optional(),
        projectCode: z.string().nullable().optional(),
        trade_code: z.number().positive().optional(),
        description: z.string().optional(),
        createdAt: z.string().optional(),
        updatedAt: z.string().optional(),
        company: z
          .string()
          .nullable()
          .optional()
          .or(
            z.object({
              code: z.string().nullable().optional(),
              name: z.string().nullable().optional(),
            }),
          ),
        project: z
          .string()
          .nullable()
          .optional()
          .or(
            z.object({
              code: z.string().nullable().optional(),
              name: z.string().nullable().optional(),
            }),
          ),
        trade: z
          .object({
            name: z.string().optional(),
            code: z.number().int().positive().optional(),
          })
          .optional(),
        itemCd: z.string().optional(),
        itmDes: z.string().optional(),
        gfq: z.number().positive().optional(),
        tradeCode: z.number().positive().optional(),
        remainingGfq: z.number().positive().nullable().optional(),
      }),
    ),
    comment: z
      .string({
        required_error: REQUIRED_FIELD_ERROR,
        invalid_type_error: 'RS Creation note must be a string',
      })
      .trim()
      // .max(100, 'Maximum of 100 Characters')
      .regex(note.REGEX, 'RS Note contains invalid characters.')
      .refine(
        (value) => value.replace(/[\r\n]/g, '').length <= 100,
        'Requisition Notes has Maximum of 100 Characters',
      )
      .optional(),
  })
  .strict();

const getRequisitionByIdSchema = z.object({
  requisitionId: z
    .string()
    .min(1, 'Requisition id is required')
    .refine((val) => val !== ':id', {
      message: 'No requisition id provided',
    }),
  canvassRequisitionId: createIdParamsSchema(
    'Canvass Requisition ID',
  ).optional(),
});

const getRequisitionByIdQuerySchema = z.object({
  canvassRequisitionId: createIdParamsSchema(
    'Canvass Requisition ID',
  ).optional(),
});

const cancelRequisitionByIdSchema = z.object({
  requisitionId: z
    .string()
    .min(1, 'Requisition id is required')
    .refine((val) => val !== ':id', {
      message: 'No requisition id provided',
    }),
});

const getRequisitionAttachmentsSchema = z.object({
  attachmentDateFrom: z
    .string()
    .optional()
    .refine((date) => !date || isoDateRegex.test(date), {
      message:
        'attachmentDateFrom must be a valid ISO 8601 date format (e.g., 2024-11-03T05:37:44.228Z)',
    }),
  attachmentDateTo: z
    .string()
    .optional()
    .refine((date) => !date || isoDateRegex.test(date), {
      message:
        'attachmentDateTo must be a valid ISO 8601 date format (e.g., 2024-11-03T05:37:44.228Z)',
    }),
});

const getRequisitionsSchema = z
  .object({
    search: z
      .string()
      .regex(note.REGEX, 'RS Search contains invalid characters.')
      .optional(),
    sortBy: sortSchema(sort.REQUISITION_SORT_COLUMNS).optional(),
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
    filterBy: filterSchema(filter.REQUISITION_FILTER_COLUMNS).optional(),
    requestType: z.enum(['my_approval', 'my_request']).optional(),
  })
  .strict();

const createRequisitionItemSchema = z.object({
  name: z.string().min(1, 'Name is required'),
  unit: z.string().min(1, 'Unit is required'), // TODO: filter allowed units
  quantity: z.number().int().positive(),
  comment: z
    .string()
    .regex(note.REGEX, 'RS Item note contains invalid characters.')
    .optional(),
});

const removeRequisitionTomItemByIdSchema = z.object({
  id: z
    .string()
    .min(1, 'Tom item id is required')
    .refine((val) => val !== ':id', {
      message: 'No requisition id provided',
    }),
});

const createRequisitionCommentSchema = z.object({
  comment: z
    .string({
      required_error: 'Comment is required',
      invalid_type_error: 'Comment must be a string',
    })
    .trim()
    .min(1, 'Comment cannot be empty')
    .max(999, 'Comment has limit of 999')
    .regex(note.REGEX, 'RS Note contains invalid characters.'),
});

const getRequisitionCommentsSchema = z.object({
  commentDateFrom: z
    .string()
    .optional()
    .refine((date) => !date || isoDateRegex.test(date), {
      message:
        'commentDateFrom must be a valid ISO 8601 date format (e.g., 2024-11-03T05:37:44.228Z)',
    }),
  commentDateTo: z
    .string()
    .optional()
    .refine((date) => !date || isoDateRegex.test(date), {
      message:
        'commentDateTo must be a valid ISO 8601 date format (e.g., 2024-11-03T05:37:44.228Z)',
    }),
});

const markCommentOrAttachmentAsSeenSchema = z.object({
  model: z.enum(['comment', 'attachment']),
  requisitionId: z.number(),
});

const updateRequisitionSchema = z
  .object({
    id: z
      .number()
      .positive()
      .optional()
      .or(z.string().transform(Number).optional()),
    type: z
      .enum(['ofm', 'non-ofm', 'ofm-tom', 'non-ofm-tom', 'transfer'], {
        message: REQUIRED_FIELD_ERROR,
      })
      .optional(),
    companyId: createIdParamsSchema(REQUIRED_FIELD_ERROR, true).optional(),
    departmentId: createIdParamsSchema(REQUIRED_FIELD_ERROR, true).optional(),
    projectId: createOptionalIdParamsSchema('Project').or(z.literal('')),
    dateRequired: z
      .string({ message: REQUIRED_FIELD_ERROR })
      .date(REQUIRED_FIELD_ERROR)
      .optional(),
    deliverTo: z
      .string({ message: REQUIRED_FIELD_ERROR })
      .min(1, REQUIRED_FIELD_ERROR)
      .optional(),
    purpose: z
      .string({ message: REQUIRED_FIELD_ERROR })
      .min(1, REQUIRED_FIELD_ERROR)
      .max(100, 'Create RS Purpose maximum 100 characters')
      .regex(note.REGEX, 'RS Purpose contains invalid characters.')
      .optional(),
    chargeTo: z
      .enum(requisition.CHARGE_TO_LIST, {
        message: REQUIRED_FIELD_ERROR,
      })
      .optional()
      .or(z.literal('')),
    chargeToId: createOptionalIdParamsSchema('Charge to ID')
      .optional()
      .or(z.literal(''))
      .or(z.undefined()),
    isDraft: z.enum(['false', 'true']).optional(),
    itemList: z
      .array(
        z
          .object({
            id: z
              .number({ message: REQUIRED_FIELD_ERROR })
              .positive()
              .optional()
              .or(z.string().transform(Number)),
            ofmListId: createNumberSchema('OFM List ID').optional(),
            quantity: z
              .number({
                errorMap: () => ({ message: 'Item quantity is required' }),
              })
              .min(0.001, 'Item quantity must be greater than 0')
              .positive()
              .or(
                z
                  .string()
                  .min(1, 'Item quantity cannot be empty')
                  .transform(Number),
              ),
            itemType: z.string().optional(),
            acctCd: z.string().optional(),
            notes: z
              .string()
              .max(500, 'Maximum of 500 Characters')
              .regex(note.REGEX, 'RS Item Note contains invalid characters.')
              .nullable()
              .optional(),
            itemName: z.string().optional(),
            unit: z.string().optional(),
            companyCode: z.number().int().positive().nullable().optional(),
            projectCode: z.string().nullable().optional(),
            trade_code: z.number().positive().optional(),
            description: z.string().optional(),
            createdAt: z.string().optional(),
            updatedAt: z.string().optional(),
            company: z
              .string()
              .nullable()
              .optional()
              .or(
                z.object({
                  code: z.string().nullable().optional(),
                  name: z.string().nullable().optional(),
                }),
              ),
            project: z
              .string()
              .nullable()
              .optional()
              .or(
                z.object({
                  code: z.string().nullable().optional(),
                  name: z.string().nullable().optional(),
                }),
              ),
            trade: z
              .object({
                name: z.string().optional(),
                code: z.number().int().positive().optional(),
              })
              .optional(),
            itemCd: z.string().optional(),
            itmDes: z.string().optional(),
            gfq: z.number().positive().optional(),
            tradeCode: z.number().positive().optional(),
            remainingGfq: z.number().positive().nullable().optional(),
          })
          .optional(),
      )
      .optional(),
    comment: z
      .string({
        invalid_type_error: 'Comment must be a string',
      })
      .trim()
      // .max(100, 'Requisition Notes has Maximum of 100 Characters')
      .regex(note.REGEX, 'RS Note contains invalid characters.')
      .refine(
        (value) => value.replace(/[\r\n]/g, '').length <= 100,
        'Requisition Notes has Maximum of 100 Characters',
      )
      .optional(),
    status: z.string().optional(),
    deliveryAddress: z.string().optional(),
    category: z
      .enum(requisition.REQUISITION_CATEGORIES, {
        message: 'Invalid category',
      })
      .optional(),
  })
  .strict();

const rejectRequisitionSchema = z.object({
  approverId: z.string().optional(),
  comments: z.array(
    z.object({
      comment: z
        .string({
          required_error: 'Notes is required',
          invalid_type_error: 'Notes must be a string',
        })
        .trim()
        .min(1, 'Notes cannot be empty')
        .max(100, 'Notes has a limit of 100')
        .regex(note.REGEX, 'RS Rejection Note contains invalid characters.'),
    }),
  ),
});

const editApproverParamsSchema = z.object({
  requisitionId: z
    .string()
    .min(1, 'Requisition id is required')
    .refine((val) => val !== ':id', {
      message: 'No requisition id provided',
    }),
  id: z
    .string()
    .min(1, 'Requisition Approver Record id is required')
    .refine((val) => val !== ':id', {
      message: 'No requisition approver record id provided',
    }),
});

const editApproverSchema = z.object({
  approverId: z
    .number()
    .positive()
    .nullable()
    .optional()
    .or(z.string().transform(Number).nullable().optional()),
  altApproverId: z
    .number()
    .positive()
    .optional()
    .or(z.string().transform(Number).optional()),
});

const requisitionApproverIdSchema = z
  .object({
    approverId: z.string().optional(),
    comment: z
      .string({
        required_error: 'Comment is required',
        invalid_type_error: 'Comment must be a string',
      })
      .transform(v => normalize(v))
      .pipe(
        z.string()
          .max(100, 'Comment has a limit of 100')
          .regex(
            note.REGEX,
            'RS Rejection Note contains invalid characters.'
          )
      )
      .optional(),
    itemList: z
      .array(
        z.object({
          id: z.number().positive().or(z.string().transform(Number)),
          quantity: z
            .number({
              errorMap: () => ({ message: 'Item Quantity Cannot Be Empty' }),
            })
            .min(0.001, 'Item Quantity Cannot Be Empty')
            .positive()
            .or(
              z
                .string()
                .min(1, 'Item Quantity Cannot Be Empty')
                .regex(note.REGEX, 'Item Quantity contains invalid characters.')
                .transform(Number),
            ),
        }),
      )
      .optional(),
  })
  .nullable()
  .optional();

const getRequisitionByIdParams = z.object({
  requisitionId: createIdParamsSchema('Requisition ID'),
});

module.exports = {
  createRequisitionSchema,
  getRequisitionByIdSchema,
  getRequisitionByIdQuerySchema,
  getRequisitionsSchema,
  requisitionSortSchema,
  createRequisitionItemSchema,
  removeRequisitionTomItemByIdSchema,
  getRequisitionAttachmentsSchema,
  createRequisitionCommentSchema,
  getRequisitionCommentsSchema,
  markCommentOrAttachmentAsSeenSchema,
  updateRequisitionSchema,
  rejectRequisitionSchema,
  requisitionApproverIdSchema,
  requisitionFilterSchema,
  getRequisitionByIdParams,
  submitRequisition,
  editApproverSchema,
  editApproverParamsSchema,
};
