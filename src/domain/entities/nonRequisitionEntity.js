const { z } = require('zod');
const {
  stringFieldError,
  createNumberSchema,
  createIdParamsSchema,
  positiveDecimalSchemaV2,
} = require('../../app/utils');
const {
  NON_RS_CHARGE_TO_LIST,
  NON_RS_APPROVER_STATUS,
  NON_RS_CATEGORIES,
} = require('../constants/nonRSConstants');
const { sort, filter, note } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { filterSchema } = require('./filterEntity');
const { normalize } = require('./utils')

const REQUIRED_FIELD_ERROR =
  'All fields are required. Kindly check your entered data';

function noteSchema(label = 'Note') {
  return z
    .string(stringFieldError(label))
    .transform(v => normalize(v))
    .pipe(
      z.string()
        .max(100, 'Note must be at most 100 characters')
        .regex(
          note.REGEX,
          'Notes can only contain letters, numbers, spaces and special characters',
        )
    )
    .optional()
}

const nonRsParams = z
  .object({
    id: createIdParamsSchema('Non-requisition ID'),
  })
  .strict();

const nonRsItemSchema = z
  .object({
    id: createNumberSchema('Item ID').optional(),
    name: z
      .string(stringFieldError('Item name'))
      .trim()
      .max(100, 'Item name must be at most 100 characters')
      .regex(
        note.REGEX,
        'Item name can only contain alphanumeric characters and special characters',
      ),
    quantity: positiveDecimalSchemaV2('Item quantity'),
    amount: positiveDecimalSchemaV2('Item amount'),
    unit: z.string(),
    discountValue: positiveDecimalSchemaV2('Discount Value', true),
    discountType: z.enum(['percent', 'fixed'], {
      message: 'Discount type must be either percent or fixed',
    }),
  })
  .strict();

const createNonRsSchema = z
  .object({
    id: createNumberSchema('Non RS ID').optional(),
    isDraft: z
      .boolean({
        invalid_type_error: 'Invalid draft status',
      })
      .optional(),
    chargeTo: z.enum(NON_RS_CHARGE_TO_LIST).optional(),
    chargeToId: createNumberSchema('Charge to (client)').optional(),
    category: z.enum(NON_RS_CATEGORIES),
    supplierId: createIdParamsSchema('Supplier'),
    companyId: createIdParamsSchema('Company'),
    departmentId: createIdParamsSchema('Department'),
    projectId: createIdParamsSchema('Project').nullable().optional(),
    invoiceNo: z
      .string(stringFieldError('Invoice number'))
      .trim()
      .max(100, 'Invoice Number must not exceed 100 characters')
      .regex(
        note.REGEX,
        'Invoice Number can only contain letters, numbers, spaces and special characters',
      ),
    invoiceNotes: noteSchema('Note'),
    groupDiscountType: z
      .enum(['percent', 'fixed'], {
        message: 'Discount type must be either percent or fixed',
      })
      .optional(),
    groupDiscountPrice: positiveDecimalSchemaV2(
      'Group Discount Price',
    ).optional(),
    supplierInvoiceAmount: positiveDecimalSchemaV2('Supplier Invoice Amount'),
    payableTo: z
      .string(stringFieldError('payable'))
      .trim()
      .max(100, 'Payable To must not exceed 100 characters')
      .regex(
        note.REGEX,
        'Payable To can only contain letters, numbers, spaces and special characters',
      ),
    invoiceDate: z
      .string(stringFieldError('Invoice Date'))
      .date(REQUIRED_FIELD_ERROR),
    itemList: z
      .array(nonRsItemSchema, {
        invalid_type_error: 'Invalid Non-RS Item list',
      })
      .optional(),
    attachmentIds: z
      .array(createNumberSchema('Attachment ID'), {
        message: 'Invalid attachment',
      })
      .optional(),
    invoiceAttachmentIds: z
      .array(createNumberSchema('Invoice attachmentIds ID'), {
        message: 'Invalid supplier attachment',
      })
      .optional(),
    notes: noteSchema('Note')
  })
  .strict();

const approveNonRsSchema = z
  .object({
    approverId: createNumberSchema('Approver ID').optional(),
    itemList: z
      .array(nonRsItemSchema, {
        invalid_type_error: 'Invalid Non-RS Item list',
      })
      .optional(),
    approveReason: noteSchema('Note')
  })
  .strict();

const rejectNonRsSchema = z
  .object({
    rejectReason: z
      .string(stringFieldError('Reject reason'))
      .min(1, { message: 'Reject reason is required' })
      .max(100, { message: 'Reject reason must not exceed 100 characters' })
      .regex(
        note.REGEX,
        'Notes can only contain letters, numbers, spaces and special characters',
      ),
  })
  .strict();

const createNonRsHistorySchema = z
  .object({
    approverId: createNumberSchema('User ID'),
    nonRequisitionId: createNumberSchema('Non RS ID'),
    status: z.enum(
      [NON_RS_APPROVER_STATUS.APPROVED, NON_RS_APPROVER_STATUS.REJECTED],
      {
        message: 'Invalid approval status',
      },
    ),
  })
  .strict();

const addAdhocApproverSchema = z
  .object({
    approverId: createNumberSchema('Approver Id'),
  })
  .strict();

const nonRSSortSchema = sortSchema(sort.NON_RS_SORT_COLUMNS);
const nonRSFilterSchema = filterSchema(filter.NON_RS_FILTER_COLUMNS);
const nonRSHistorySortSchema = sortSchema(sort.NON_RS_HISTORY_SORT_COLUMNS);
const nonRSHistoryFilterSchema = filterSchema(
  filter.NON_RS_HISTORY_FILTER_COLUMNS,
);

module.exports = {
  nonRsParams,
  rejectNonRsSchema,
  createNonRsSchema,
  approveNonRsSchema,
  createNonRsHistorySchema,
  nonRSSortSchema,
  nonRSFilterSchema,
  nonRSHistorySortSchema,
  nonRSHistoryFilterSchema,
  addAdhocApproverSchema,
};
